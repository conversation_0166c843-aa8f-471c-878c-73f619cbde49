<template>
    <div class="h-full pb-1 pr-1 text-sm">
        <div class="h-full w-full flex flex-col pb-1">
            <div class="flex w-full gap-1 h-[140px]">
                <div class="bg-white rounded-md h-full flex-grow p-4">
                    <div>账号：{{ userInfo?.email }}</div>
                    <div class="flex mt-2 items-center">
                        <div>余额：{{ userInfo?.pointsBalance }}</div>
                        <div class="px-2 cursor-pointer" @click="userStore.getUserInfo">
                            <el-icon>
                                <Refresh />
                            </el-icon>
                        </div>
                        <div>
                            <el-button type="primary" size="small" @click="openRechargeDialog">充值</el-button>
                        </div>
                    </div>
                </div>
                <div v-if="false" class="bg-white rounded-md h-full w-[500px]"></div>
            </div>
            <div class="flex-grow bg-white rounded-md mt-1"></div>
        </div>
    </div>
    <RechargeDialog v-model="rechargeDialogVisible" />
</template>
<script lang="ts" setup>
import { useUserInfoStore } from '@/stores/user-info';
import { Refresh } from '@element-plus/icons-vue'; // 刷新图标
import { computed, ref } from 'vue';
import RechargeDialog from '@/components/RechargeDialog/index.vue';

const userStore = useUserInfoStore();
const userInfo = computed(() => userStore.userInfo);

// 充值对话框可见性
const rechargeDialogVisible = ref(false);

// 打开充值对话框
const openRechargeDialog = () => {
  rechargeDialogVisible.value = true;
};
</script>