{"name": "giant-whale-ai-server", "version": "1.0.0", "description": "巨鲸AI翻译服务端", "main": "src/index.ts", "scripts": {"prod": "pm2 stop giant-whale-ai-server && npm run build && pm2 start dist/index.js --name giant-whale-ai-server", "start": "ts-node src/index.ts", "dev": "nodemon --exec ts-node src/index.ts", "build": "cross-env NODE_ENV=production webpack --config webpack.config.js", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "format": "prettier --write \"src/**/*.ts\"", "prepare": "husky install", "typeorm": "typeorm-ts-node-commonjs", "migration:generate": "npm run typeorm migration:generate -- -d src/config/database.ts", "migration:run": "npm run typeorm migration:run -- -d src/config/database.ts", "migration:revert": "npm run typeorm migration:revert -- -d src/config/database.ts", "import-points-products": "ts-node scripts/import-points-products.ts"}, "dependencies": {"@alicloud/alimt20181012": "^1.4.1", "@alicloud/openapi-client": "^0.4.15", "@alicloud/tea-util": "^1.4.10", "@koa/cors": "^4.0.0", "@koa/router": "^12.0.1", "@types/koa-router": "^7.4.8", "axios": "^1.9.0", "bcryptjs": "^2.4.3", "class-validator": "^0.14.0", "dotenv": "^16.3.1", "ioredis": "^5.7.0", "jsonwebtoken": "^9.0.2", "koa": "^2.14.2", "koa-bodyparser": "^4.4.1", "koa-router": "^13.0.1", "koa-swagger-decorator": "^1.8.7", "mysql2": "^3.6.5", "nodemailer": "^6.10.0", "reflect-metadata": "^0.2.2", "swagger-ui-dist": "^5.20.2", "trek-captcha": "^0.4.0", "typeorm": "^0.3.17", "utility": "^2.5.0"}, "devDependencies": {"@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/koa": "^2.13.12", "@types/koa__cors": "^4.0.3", "@types/koa__router": "^12.0.4", "@types/koa-bodyparser": "^4.3.12", "@types/node": "^20.10.4", "@types/nodemailer": "^6.4.17", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "copy-webpack-plugin": "^13.0.1", "cross-env": "^10.0.0", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "file-loader": "^6.2.0", "husky": "^8.0.3", "nodemon": "^3.0.2", "prettier": "^3.1.0", "terser-webpack-plugin": "^5.3.14", "ts-loader": "^9.5.1", "ts-node": "^10.9.1", "typescript": "^5.3.3", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "webpack-node-externals": "^3.0.0"}}