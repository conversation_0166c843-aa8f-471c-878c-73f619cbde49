# 服务器配置
PORT=3000
NODE_ENV=development

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=x-scrm-test
DB_PASSWORD=x-scrm-test
DB_DATABASE=x-scrm-test

# JWT配置
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRATION=7d

# 邮件配置
SMTP_HOST=smtp.163.com
SMTP_PORT=465
SMTP_SECURE=true
SMTP_USER=<EMAIL>
SMTP_PASS=XJR3RNmBPw4AufP4
SMTP_FROM=<EMAIL>

# 前端URL
ADMIN_URL=http://localhost:8080
CLIENT_URL=http://localhost:8081 

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Captcha
CAPTCHA_EXPIRE_TIME=300
CAPTCHA_SIZE=4
CAPTCHA_NOISE=2
CAPTCHA_COLOR=true
CAPTCHA_BACKGROUND=#f0f0f0

# 验证码前缀
CAPTCHA_PREFIX=captcha:

# 验证码过期时间
CAPTCHA_EXPIRE_TIME=300

# ZPay Configuration (测试配置，请替换为实际值)
ZPAY_PID=2025073010063913
ZPAY_KEY=JrLDNgdmt5ZYNNq3FJ3fYOL43m35uIFq
ZPAY_BASE_URL=https://z-pay.cn
ZPAY_NOTIFY_URL=http://api.lwle.cn/api/client/points/payment/notify
ZPAY_RETURN_URL=http://api.lwle.cn/payment/success
ZPAY_SITENAME=巨鲸AI
