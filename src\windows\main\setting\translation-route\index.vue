<template>
    <div class="p-2 w-full h-full overflow-y-auto">
        <div v-for="config in TRANSLATION_ROUTE_CONFIGS" :key="config.type" class="w-full"
            :class="{ 'mt-6': config.type !== TranslationType.TEXT }">
            <div>
                <div class="font-bold">{{ config.title }}</div>
                <div class="text-xs pt-3 w-[600px]">{{ config.description }}</div>
            </div>
            <div class="pt-3 w-full">
                <div class="w-[700px]">
                    <el-table :data="getDataByType(config.type)" border size="small" empty-text="暂无数据">
                        <el-table-column prop="vendor" label="线路供应商" width="130" align="center">
                            <template #default="scope">
                                {{ VENDOR_LABELS[scope.row.vendor as VendorType] }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="apiKey" label="APIKEY" />
                        <el-table-column prop="apiSecret" label="APISECRET" />
                        <el-table-column label="线路权限" width="80" align="center">
                            <template #default="scope">
                                {{ scope.row.perm === 1 ? '私有线路' : '公共线路' }}
                            </template>
                        </el-table-column>
                        <el-table-column v-if="config.type === TranslationType.TEXT" label="测试线路" width="80"
                            align="center">
                            <template #default="scope">
                                <el-button size="small" type="text"
                                    @click="translationTestDialogRef.open(scope.row.vendor)">测试</el-button>
                            </template>
                        </el-table-column>
                        <el-table-column label="是否启用" width="80" align="center">
                            <template #default="scope">
                                <el-switch v-model="scope.row.isActive" @change="handleToggleStatus(scope.row)" />
                            </template>
                        </el-table-column>
                        <el-table-column width="120" align="center">
                            <template #header>
                                <el-button size="small" type="primary"
                                    @click="translationRouteDialogRef.open(undefined, accountId, config.type)">新增</el-button>
                            </template>
                            <template #default="scope">
                                <el-button size="small" type="text"
                                    @click="translationRouteDialogRef.open(scope.row, accountId, config.type)">编辑</el-button>
                                <el-button size="small" type="danger" link
                                    @click="handleDelete(scope.row)">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
        </div>
    </div>
    <TranslationRouteDialog ref="translationRouteDialogRef" @success="handleSuccess"></TranslationRouteDialog>
    <TranslationTestDialog ref="translationTestDialogRef"></TranslationTestDialog>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import TranslationRouteDialog from './components/TranslationRouteDialog/index.vue'
import TranslationTestDialog from './components/TranslationTest/index.vue'
import { getTranslationRouteListAPI, toggleTranslationRouteStatusAPI, deleteTranslationRouteAPI, type ITranslationRoute, TranslationType, VendorType } from '@/api/translation-route'
import { TRANSLATION_ROUTE_CONFIGS, VENDOR_LABELS } from '@/constants/translation-route'

const translationRouteDialogRef = ref()
const translationTestDialogRef = ref()
const routeData = ref<ITranslationRoute[]>([])
const accountId = ref('') // 需要从外部传入

const getDataByType = (type: TranslationType) => {
    return routeData.value.filter(item => item.type === type)
}

const fetchData = async () => {
    try {
        const res = await getTranslationRouteListAPI()
        routeData.value = res.data
    } catch (error) {
        console.error('获取翻译路由列表失败:', error)
        ElMessage.error('获取翻译路由列表失败')
    }
}

const handleToggleStatus = async (row: ITranslationRoute) => {
    try {
        await toggleTranslationRouteStatusAPI(row.id)
        ElMessage.success('状态更新成功')
    } catch (error) {
        console.error('状态更新失败:', error)
        ElMessage.error('状态更新失败')
        row.isActive = !row.isActive // 恢复状态
    }
}

const handleDelete = async (row: ITranslationRoute) => {
    try {
        await ElMessageBox.confirm('确定要删除该翻译路由吗？', '提示', {
            type: 'warning'
        })
        await deleteTranslationRouteAPI(row.id)
        ElMessage.success('删除成功')
        fetchData()
    } catch (error) {
        if (error !== 'cancel') {
            console.error('删除失败:', error)
            ElMessage.error('删除失败')
        }
    }
}

const handleSuccess = () => {
    fetchData()
}

onMounted(() => {
    fetchData()
})
</script>
