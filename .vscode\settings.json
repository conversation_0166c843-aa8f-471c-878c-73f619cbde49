{"typescript.tsdk": "node_modules/typescript/lib", "typescript.tsc.autoDetect": "off", "json.schemas": [{"fileMatch": ["/*electron-builder.json5", "/*electron-builder.json"], "url": "https://json.schemastore.org/electron-builder"}], "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "[vue]": {"editor.defaultFormatter": "Vue.volar"}, "[typescript]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "eslint.validate": ["javascript", "typescript", "vue"], "[scss]": {"editor.defaultFormatter": "vscode.css-language-features"}}