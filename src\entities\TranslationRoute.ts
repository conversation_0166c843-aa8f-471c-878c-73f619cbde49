import { Entity, Column } from 'typeorm';
import { BaseEntity } from './BaseEntity';

export enum TranslationType {
  TEXT = 'text',
  AUDIO = 'audio',
  VIDEO = 'video',
  IMAGE = 'image',
  DOCUMENT = 'document',
}

export enum VendorType {
  GOOGLE = 'google',
  BAIDU = 'baidu',
  YOUDAO = 'youdao',
  DEEPL = 'deepl',
  ALIBABA = 'alibaba',
}

@Entity()
export class TranslationRoute extends BaseEntity {
  @Column({
    type: 'enum',
    enum: TranslationType,
    comment: '翻译类型',
  })
  type: TranslationType;

  @Column({
    type: 'enum',
    enum: VendorType,
    comment: '供应商类型',
  })
  vendor: VendorType;

  @Column({ comment: 'API访问密钥' })
  apiKey: string;

  @Column({ comment: 'API密钥', nullable: true })
  apiSecret?: string;

  @Column({ default: true, comment: '是否启用' })
  isActive: boolean;

  @Column({ default: 0, comment: '排序权重' })
  sort: number;

  @Column({ name: 'account_id', comment: '关联账号ID' })
  accountId: string;

  @Column({ default: 1, comment: '线路使用权限：1 私有权限 2 公共权限' })
  perm: number;
}
