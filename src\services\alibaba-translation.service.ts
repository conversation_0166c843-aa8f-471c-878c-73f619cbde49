import { TranslationRouteService } from './translation-route.service';
import { TranslationRoute } from '../entities/TranslationRoute';
import { TranslationType, VendorType } from '../entities/TranslationRoute';
import Alimt20181012, * as $Alimt20181012 from '@alicloud/alimt20181012';
import * as $OpenApi from '@alicloud/openapi-client';
import * as $Util from '@alicloud/tea-util';
import { PointsService } from './points.service';
import { PointsTransactionReferenceType } from '../entities/PointsTransaction';

// 阿里云图片翻译响应接口
interface AlibabaTranslateImageResponse {
  RequestId: string;
  Code: number;
  Message: string;
  Data: {
    FinalImageUrl: string;
    InPaintingUrl?: string;
    TemplateJson?: string;
  };
}

export class AlibabaTranslationService {
  private translationRouteService: TranslationRouteService;
  private pointsService: PointsService;

  constructor() {
    this.translationRouteService = new TranslationRouteService();
    this.pointsService = new PointsService();
  }

  /**
   * 获取所有可用的阿里云图片翻译线路
   */
  async getAvailableAlibabaImageRoutes(accountId: string): Promise<TranslationRoute[]> {
    const routes = await this.translationRouteService.getRoutesByAccount(accountId);
    return routes.filter(
      (route) =>
        route.vendor === VendorType.ALIBABA &&
        route.type === TranslationType.IMAGE &&
        route.isActive,
    );
  }

  /**
   * 创建阿里云客户端
   */
  private createClient(accessKeyId: string, accessKeySecret: string): $Alimt20181012.default {
    const config = new $OpenApi.Config({
      accessKeyId,
      accessKeySecret,
      regionId: 'cn-hangzhou',
    });
    // 设置访问的域名
    config.endpoint = 'mt.cn-hangzhou.aliyuncs.com';
    return new $Alimt20181012.default(config);
  }

  /**
   * 将语言代码转换为阿里云支持的格式
   */
  private convertLanguageCode(langCode: string): string {
    const languageMap: { [key: string]: string } = {
      zh: 'zh',
      'zh-CN': 'zh',
      'zh-CHS': 'zh',
      en: 'en',
      ja: 'ja',
      ko: 'ko',
      fr: 'fr',
      es: 'es',
      it: 'it',
      de: 'de',
      tr: 'tr',
      ru: 'ru',
      pt: 'pt',
      vi: 'vi',
      id: 'id',
      th: 'th',
      ms: 'ms',
      ar: 'ar',
      hi: 'hi',
    };
    return languageMap[langCode] || langCode;
  }

  /**
   * 执行图片翻译
   */
  async translateImage(
    imageBase64: string,
    from: string,
    to: string,
    route: TranslationRoute,
  ): Promise<string | null> {
    try {
      console.log('开始阿里云图片翻译，参数:', {
        from,
        to,
        routeId: route.id,
        imageSize: imageBase64.length,
        apiKey: route.apiKey,
        apiSecret: route.apiSecret,
      });

      const client = this.createClient(route.apiKey, route.apiSecret || '');

      // 转换语言代码
      const sourceLanguage = this.convertLanguageCode(from);
      const targetLanguage = this.convertLanguageCode(to);

      console.log('语言转换:', { from, to, sourceLanguage, targetLanguage });

      // 阿里云图片翻译特殊限制：仅支持原图为中/英文的图片翻译成其他语言
      if (sourceLanguage !== 'zh' && sourceLanguage !== 'en') {
        throw new Error(
          `阿里云图片翻译仅支持中文(zh)或英文(en)作为源语言，当前源语言: ${sourceLanguage}。请使用有道翻译进行其他语言的图片翻译。`,
        );
      }

      // 验证必需参数
      if (!sourceLanguage || !targetLanguage) {
        throw new Error(
          `语言代码无效: from=${from}, to=${to}, sourceLanguage=${sourceLanguage}, targetLanguage=${targetLanguage}`,
        );
      }

      // 移除 data:image/xxx;base64, 前缀（如果存在）
      const base64Data = imageBase64.replace(/^data:image\/[a-z]+;base64,/, '');

      // 验证Base64数据
      if (!base64Data || base64Data.length === 0) {
        throw new Error('Base64图片数据为空');
      }

      // 验证Base64格式
      const base64Regex = /^[A-Za-z0-9+\/]*={0,2}$/;
      if (!base64Regex.test(base64Data)) {
        throw new Error('Base64数据格式无效');
      }

      // 检查图片大小（阿里云限制10MB）
      const imageSizeBytes = (base64Data.length * 3) / 4;
      const imageSizeMB = imageSizeBytes / (1024 * 1024);
      if (imageSizeMB > 10) {
        throw new Error(`图片大小超出限制: ${imageSizeMB.toFixed(2)}MB > 10MB`);
      }

      console.log('准备调用阿里云API，参数:', {
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
        field: 'general',
        imageBase64Length: base64Data.length,
        imageSizeMB: ((base64Data.length * 3) / 4 / (1024 * 1024)).toFixed(2),
        hasImageBase64: !!base64Data,
      });

      const translateImageRequest = new $Alimt20181012.TranslateImageRequest({
        imageBase64: base64Data,
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
        field: 'general', // 改回general，e-commerce可能需要特殊权限
      });

      const runtime = new $Util.RuntimeOptions({
        autoretry: false,
        ignoreSSL: false,
        maxIdleConns: 50,
        maxIdleTimeMillis: 5000,
        keepAliveDurationMillis: 5000,
        maxRequests: 100,
        maxRequestsPerHost: 100,
        readTimeout: 30000,
        connectTimeout: 30000,
      });

      console.log('开始调用阿里云API...');
      const response = await client.translateImageWithOptions(translateImageRequest, runtime);
      console.log('阿里云API调用完成');

      // 详细的响应日志
      console.log('阿里云API完整响应:', {
        statusCode: response.statusCode,
        headers: response.headers,
        bodyType: typeof response.body,
        bodyKeys: response.body ? Object.keys(response.body) : 'null',
        fullBody: response.body?.data?.finalImageUrl,
      });

      // 尝试多种可能的响应结构
      let message: string | undefined, data: any;

      // 检查其他可能的成功标识
      if (response.statusCode === 200) {
        const imageUrl = response.body?.data?.finalImageUrl || null;
        const result = `图片翻译完成。翻译后的图片URL: ${imageUrl}`;
        console.log(result);
        return imageUrl;
      }

      const errorMsg = message || '阿里云API响应格式异常';
      console.error('阿里云图片翻译失败:', {
        errorMsg,
        responseStatus: response.statusCode,
        hasBody: !!response.body,
        bodyType: typeof response.body,
      });

      // 如果响应结构完全异常，提供更详细的错误信息
      if (!response.body || typeof response.body !== 'object') {
        console.error('阿里云API返回了无效的响应结构');
        return null;
      }

      return null;
    } catch (error: any) {
      console.error('阿里云图片翻译请求失败:', {
        message: error.message,
        code: error.code,
        data: error.data,
        statusCode: error.statusCode,
        requestId: error.requestId,
      });
      return null;
    }
  }

  /**
   * 智能图片翻译（失败时自动切换线路）
   */
  async smartTranslateImage(
    imageBase64: string,
    from: string,
    to: string,
    accountId: string,
  ): Promise<string | null> {
    const routes = await this.getAvailableAlibabaImageRoutes(accountId);

    if (routes.length === 0) {
      throw new Error('没有可用的阿里云图片翻译线路');
    }

    // 先扣除积分
    let pointsTransaction;
    try {
      pointsTransaction = await this.pointsService.consumePoints({
        clientId: accountId,
        vendor: VendorType.ALIBABA,
        type: TranslationType.IMAGE,
        description: `阿里云图片翻译：${from} -> ${to}`,
        referenceType: PointsTransactionReferenceType.TRANSLATION,
        metadata: {
          from,
          to,
          vendor: VendorType.ALIBABA,
          type: TranslationType.IMAGE,
          imageSize: imageBase64.length,
        },
      });
    } catch (error: any) {
      throw new Error(`积分扣除失败: ${error.message}`);
    }

    // 尝试每条线路，直到成功或全部失败
    for (const route of routes) {
      const result = await this.translateImage(imageBase64, from, to, route);
      if (result !== null) {
        return result;
      }
      console.log(`阿里云图片翻译线路 ${route.id} 失败，尝试下一条线路`);
    }

    // 所有线路都失败，退还积分
    try {
      await this.pointsService.refundPoints({
        clientId: accountId,
        amount: Math.abs(pointsTransaction.amount),
        description: `阿里云图片翻译失败，退还积分`,
        referenceType: PointsTransactionReferenceType.TRANSLATION,
        referenceId: pointsTransaction.id,
        metadata: {
          originalTransactionId: pointsTransaction.id,
          reason: '所有阿里云图片翻译线路均不可用',
        },
      });
    } catch (refundError) {
      console.error('积分退还失败:', refundError);
    }

    throw new Error('所有阿里云图片翻译线路均不可用');
  }

  /**
   * 获取支持的语言列表
   * @param type 'source' | 'target' - 源语言或目标语言
   */
  getSupportedLanguages(type: 'source' | 'target' = 'target'): { [key: string]: string } {
    // 阿里云图片翻译的源语言限制：仅支持中文和英文
    if (type === 'source') {
      return {
        zh: '中文',
        en: '英语',
      };
    }

    // 目标语言支持所有语言
    return {
      zh: '中文',
      en: '英语',
      ja: '日语',
      ko: '韩语',
      fr: '法语',
      es: '西班牙语',
      it: '意大利语',
      de: '德语',
      tr: '土耳其语',
      ru: '俄语',
      pt: '葡萄牙语',
      vi: '越南语',
      id: '印尼语',
      th: '泰语',
      ms: '马来语',
      ar: '阿拉伯语',
      hi: '印地语',
    };
  }
}
