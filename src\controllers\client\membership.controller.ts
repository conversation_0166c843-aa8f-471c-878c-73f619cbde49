import { Context } from 'koa';
import { MembershipProductService } from '../../services/membership-product.service';
import { MembershipOrderService } from '../../services/membership-order.service';
import { ClientMembershipService } from '../../services/client-membership.service';
import { ZPayService, ZPayConfig } from '../../services/zpay.service';
import {
  ZPAY_PID,
  ZPAY_KEY,
  ZPAY_BASE_URL,
  ZPAY_MEMBERSHIP_NOTIFY_URL,
  ZPAY_MEMBERSHIP_RETURN_URL,
  ZPAY_SITENAME,
} from '../../config/z-pay';

interface CreateOrderBody {
  productId: string;
}

interface PayOrderBody {
  orderNo: string;
  paymentMethod: 'alipay' | 'wxpay';
}

interface CheckPermissionBody {
  permissionKey: string;
}

/**
 * 客户端会员控制器
 */
export class ClientMembershipController {
  private membershipProductService: MembershipProductService;
  private membershipOrderService: MembershipOrderService;
  private clientMembershipService: ClientMembershipService;
  private zPayService: ZPayService;

  constructor() {
    this.membershipProductService = new MembershipProductService();
    this.membershipOrderService = new MembershipOrderService();
    this.clientMembershipService = new ClientMembershipService();

    // 创建ZPay服务实例，使用会员订单专用的回调URL
    const membershipPayConfig: ZPayConfig = {
      pid: ZPAY_PID,
      key: ZPAY_KEY,
      baseUrl: ZPAY_BASE_URL,
      notifyUrl: ZPAY_MEMBERSHIP_NOTIFY_URL,
      returnUrl: ZPAY_MEMBERSHIP_RETURN_URL,
      sitename: ZPAY_SITENAME,
    };

    this.zPayService = new ZPayService(membershipPayConfig);
  }

  /**
   * 获取会员商品列表
   */
  public getProductList = async (ctx: Context): Promise<void> => {
    try {
      const products = await this.membershipProductService.getActiveMembershipProducts();

      ctx.body = {
        code: 0,
        data: products,
        message: '获取会员商品列表成功',
      };
    } catch (error) {
      console.error('获取会员商品列表失败', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '获取会员商品列表失败',
      };
    }
  };

  /**
   * 创建会员订单
   */
  public createOrder = async (ctx: Context): Promise<void> => {
    try {
      const { productId } = ctx.request.body as CreateOrderBody;

      if (!productId) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '请选择会员商品',
        };
        return;
      }

      // 获取当前用户ID
      const clientId = ctx.state.user.id;

      // 创建订单
      const order = await this.membershipOrderService.createOrder(clientId, productId);

      ctx.body = {
        code: 0,
        data: order,
        message: '创建订单成功',
      };
    } catch (error) {
      console.error('创建订单失败', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error instanceof Error ? error.message : '创建订单失败',
      };
    }
  };

  /**
   * 获取订单列表
   */
  public getOrderList = async (ctx: Context): Promise<void> => {
    try {
      // 获取当前用户ID
      const clientId = ctx.state.user.id;

      // 获取订单状态筛选
      const status = ctx.query.status as string;

      // 获取订单列表
      const orders = await this.membershipOrderService.getClientOrders(clientId, status);

      ctx.body = {
        code: 0,
        data: orders,
        message: '获取订单列表成功',
      };
    } catch (error) {
      console.error('获取订单列表失败', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '获取订单列表失败',
      };
    }
  };

  /**
   * 创建支付链接
   */
  public payOrder = async (ctx: Context): Promise<void> => {
    try {
      const { orderNo, paymentMethod } = ctx.request.body as PayOrderBody;

      if (!orderNo || !paymentMethod) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '请提供订单号和支付方式',
        };
        return;
      }

      // 查找订单
      const order = await this.membershipOrderService.findByOrderNo(orderNo);
      if (!order) {
        ctx.status = 404;
        ctx.body = {
          code: 404,
          message: '订单不存在',
        };
        return;
      }

      if (order.status !== 'pending') {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '订单状态不正确',
        };
        return;
      }

      // 创建支付链接
      const result = await this.zPayService.createPaymentQrCode({
        orderNo: order.orderNo,
        money: order.amount / 100, // 转换为元
        name: order.membershipProduct.name,
        type: paymentMethod,
        clientip: ctx.request.ip || '127.0.0.1',
      });

      ctx.body = {
        code: 0,
        data: result,
        message: '支付链接创建成功',
      };
    } catch (error) {
      console.error('创建支付链接失败', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error instanceof Error ? error.message : '创建支付链接失败',
      };
    }
  };

  /**
   * 支付回调通知
   */
  public paymentNotify = async (ctx: Context): Promise<void> => {
    try {
      const notifyData = ctx.request.body as any;

      // 验证回调数据
      const verifyResult = this.zPayService.verifyNotify(notifyData);

      if (!verifyResult.isValid) {
        ctx.status = 400;
        ctx.body = 'FAIL';
        return;
      }

      if (!verifyResult.isSuccess || !verifyResult.orderNo || !verifyResult.tradeNo) {
        ctx.body = 'SUCCESS'; // 即使支付失败也要返回SUCCESS，避免重复通知
        return;
      }

      // 处理支付成功
      await this.membershipOrderService.handlePaymentSuccess(
        verifyResult.orderNo,
        notifyData.type,
        verifyResult.tradeNo,
      );

      ctx.body = 'SUCCESS';
    } catch (error) {
      console.error('处理支付回调失败:', error);
      ctx.status = 500;
      ctx.body = 'FAIL';
    }
  };

  /**
   * 获取当前会员状态
   */
  public getMembershipStatus = async (ctx: Context): Promise<void> => {
    try {
      // 获取当前用户ID
      const clientId = ctx.state.user.id;

      // 获取当前会员状态
      const membership = await this.clientMembershipService.getCurrentMembership(clientId);

      ctx.body = {
        code: 0,
        data: {
          hasMembership: !!membership,
          membership,
        },
        message: '获取会员状态成功',
      };
    } catch (error) {
      console.error('获取会员状态失败', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '获取会员状态失败',
      };
    }
  };

  /**
   * 检查权限
   */
  public checkPermission = async (ctx: Context): Promise<void> => {
    try {
      // 获取当前用户ID
      const clientId = ctx.state.user.id;

      // 获取权限标识
      const { permissionKey } = ctx.request.body as CheckPermissionBody;

      if (!permissionKey) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '请提供权限标识',
        };
        return;
      }

      // 检查权限
      const hasPermission = await this.clientMembershipService.checkClientPermission(
        clientId,
        permissionKey,
      );

      ctx.body = {
        code: 0,
        data: {
          hasPermission,
        },
        message: '检查权限成功',
      };
    } catch (error) {
      console.error('检查权限失败', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '检查权限失败',
      };
    }
  };
}
