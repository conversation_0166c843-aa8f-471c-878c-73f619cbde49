import { Context } from 'koa';
import { PointsService } from '../../services/points.service';
import { PointsProductService } from '../../services/points-product.service';
import { PointsOrderService } from '../../services/points-order.service';
import { ZPayService } from '../../services/zpay.service';

interface CreateOrderRequest {
  productId: string;
}

interface PayOrderRequest {
  orderNo: string;
  paymentMethod: 'alipay' | 'wxpay' | 'qqpay' | 'tenpay';
}

/**
 * 积分控制器
 */
export class PointsController {
  private pointsService: PointsService;
  private pointsProductService: PointsProductService;
  private pointsOrderService: PointsOrderService;
  private zPayService: ZPayService;

  constructor() {
    this.pointsService = new PointsService();
    this.pointsProductService = new PointsProductService();
    this.pointsOrderService = new PointsOrderService();
    this.zPayService = ZPayService.createFromEnv();
  }

  /**
   * 获取积分商品列表
   */
  async getProducts(ctx: Context): Promise<void> {
    try {
      const products = await this.pointsProductService.findActiveProducts();

      ctx.body = {
        code: 200,
        data: products,
        message: '获取成功',
      };
    } catch (error) {
      console.error('获取积分商品失败:', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error instanceof Error ? error.message : '获取积分商品失败',
      };
    }
  }

  /**
   * 获取用户积分余额
   */
  async getBalance(ctx: Context): Promise<void> {
    try {
      const { user } = ctx.state;
      const balance = await this.pointsService.getBalance(user.id);

      ctx.body = {
        code: 200,
        data: { balance },
        message: '获取成功',
      };
    } catch (error) {
      console.error('获取积分余额失败:', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error instanceof Error ? error.message : '获取积分余额失败',
      };
    }
  }

  /**
   * 获取积分流水记录
   */
  async getTransactions(ctx: Context): Promise<void> {
    try {
      const { user } = ctx.state;
      const { page = 1, limit = 20 } = ctx.query;

      const result = await this.pointsService.getTransactions(user.id, Number(page), Number(limit));

      ctx.body = {
        code: 200,
        data: result,
        message: '获取成功',
      };
    } catch (error) {
      console.error('获取积分流水失败:', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error instanceof Error ? error.message : '获取积分流水失败',
      };
    }
  }

  /**
   * 创建积分订单
   */
  async createOrder(ctx: Context): Promise<void> {
    try {
      const { user } = ctx.state;
      const { productId } = ctx.request.body as CreateOrderRequest;

      if (!productId) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '请选择积分商品',
        };
        return;
      }

      const order = await this.pointsOrderService.createOrder({
        clientId: user.id,
        productId,
      });

      ctx.body = {
        code: 200,
        data: order,
        message: '订单创建成功',
      };
    } catch (error) {
      console.error('创建积分订单失败:', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error instanceof Error ? error.message : '创建积分订单失败',
      };
    }
  }

  /**
   * 获取用户订单列表
   */
  async getOrders(ctx: Context): Promise<void> {
    try {
      const { user } = ctx.state;
      const { page = 1, limit = 20 } = ctx.query;

      const result = await this.pointsOrderService.findByClientId(
        user.id,
        Number(page),
        Number(limit),
      );

      ctx.body = {
        code: 200,
        data: result,
        message: '获取成功',
      };
    } catch (error) {
      console.error('获取订单列表失败:', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error instanceof Error ? error.message : '获取订单列表失败',
      };
    }
  }

  /**
   * 创建支付链接
   */
  async createPayment(ctx: Context): Promise<void> {
    try {
      const { orderNo, paymentMethod } = ctx.request.body as PayOrderRequest;

      if (!orderNo || !paymentMethod) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '请提供订单号和支付方式',
        };
        return;
      }

      // 查找订单
      const order = await this.pointsOrderService.findByOrderNo(orderNo);
      if (!order) {
        ctx.status = 404;
        ctx.body = {
          code: 404,
          message: '订单不存在',
        };
        return;
      }

      if (order.status !== 'pending') {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '订单状态不正确',
        };
        return;
      }

      if (order.isExpired) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '订单已过期',
        };
        return;
      }

      // 创建支付链接
      const paymentUrl = this.zPayService.createPaymentUrl({
        orderNo: order.orderNo,
        money: order.actualPrice / 100, // 转换为元
        name: order.product.name,
        type: paymentMethod,
      });

      ctx.body = {
        code: 200,
        data: {
          paymentUrl,
          orderNo: order.orderNo,
          amount: order.actualPrice,
          productName: order.product.name,
        },
        message: '支付链接创建成功',
      };
    } catch (error) {
      console.error('创建支付链接失败:', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error instanceof Error ? error.message : '创建支付链接失败',
      };
    }
  }

  /**
   * 支付回调通知
   */
  async paymentNotify(ctx: Context): Promise<void> {
    try {
      const notifyData = ctx.request.body as any;

      // 验证回调数据
      const verifyResult = this.zPayService.verifyNotify(notifyData);

      if (!verifyResult.isValid) {
        ctx.status = 400;
        ctx.body = 'FAIL';
        return;
      }

      if (!verifyResult.isSuccess || !verifyResult.orderNo || !verifyResult.tradeNo) {
        ctx.body = 'SUCCESS'; // 即使支付失败也要返回SUCCESS，避免重复通知
        return;
      }

      // 处理支付成功
      await this.pointsOrderService.handlePaymentSuccess(
        verifyResult.orderNo,
        notifyData.type,
        verifyResult.tradeNo,
      );

      ctx.body = 'SUCCESS';
    } catch (error) {
      console.error('处理支付回调失败:', error);
      ctx.status = 500;
      ctx.body = 'FAIL';
    }
  }

  /**
   * 取消订单
   */
  async cancelOrder(ctx: Context): Promise<void> {
    try {
      const { orderNo } = ctx.params;

      const order = await this.pointsOrderService.cancelOrder(orderNo);

      ctx.body = {
        code: 200,
        data: order,
        message: '订单已取消',
      };
    } catch (error) {
      console.error('取消订单失败:', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error instanceof Error ? error.message : '取消订单失败',
      };
    }
  }
}
