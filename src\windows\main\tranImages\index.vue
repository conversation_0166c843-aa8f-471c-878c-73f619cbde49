<template>
    <div class="h-full pb-1 pr-1">
        <div class="h-full flex flex-col bg-white rounded-lg pb-1">
            <!-- 翻译设置区域 -->
            <div class="p-4">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <label class="text-sm font-medium text-gray-700">翻译线路:</label>
                        <el-select v-model="translateConfig.vendor" placeholder="请选择供应商" size="small"
                            style="width: 120px" @change="handleVendorChange">
                            <el-option v-for="vendor in imageVendors" :key="vendor.value" :label="vendor.label"
                                :value="vendor.value" />
                        </el-select>
                    </div>
                    <div class="flex items-center space-x-2">
                        <label class="text-sm font-medium text-gray-700">源语言:</label>
                        <el-select v-model="translateConfig.from" placeholder="请选择源语言" size="small"
                            style="width: 100px">
                            <el-option v-for="lang in sourceLanguages" :key="lang.value" :label="lang.label"
                                :value="lang.value" />
                        </el-select>
                    </div>
                    <div class="flex items-center space-x-2">
                        <label class="text-sm font-medium text-gray-700">目标语言:</label>
                        <el-select v-model="translateConfig.to" placeholder="请选择目标语言" size="small" style="width: 100px">
                            <el-option v-for="lang in targetLanguages" :key="lang.value" :label="lang.label"
                                :value="lang.value" />
                        </el-select>
                    </div>
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="flex-1 flex p-4 space-x-4 min-h-0">
                <!-- 原图区域 -->
                <div class="flex-1 bg-white rounded-lg border p-4 flex flex-col">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">原图</h3>
                    <div class="flex-1 flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-lg"
                        @drop="handleDrop" @dragover.prevent @dragenter.prevent>
                        <div v-if="!selectedImage" class="text-center">
                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none"
                                viewBox="0 0 48 48">
                                <path
                                    d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                            <div class="mt-4">
                                <label for="file-upload" class="cursor-pointer">
                                    <span class="mt-2 block text-sm font-medium text-gray-900">点击选择图片或拖拽到此处</span>
                                    <span class="mt-1 block text-xs text-gray-500">支持 PNG, JPG, GIF, BMP 格式，最大
                                        5MB</span>
                                </label>
                                <input id="file-upload" name="file-upload" type="file" class="sr-only" accept="image/*"
                                    @change="handleFileSelect">
                            </div>
                        </div>
                        <div v-else class="w-full h-full flex flex-col">
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm text-gray-600">{{ selectedImage.name }}</span>
                                <el-button size="small" type="danger" @click="clearImage">清除</el-button>
                            </div>
                            <div class="flex-1 flex items-center justify-center">
                                <img :src="imagePreview" alt="预览图片"
                                    class="max-w-full max-h-full object-contain rounded">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 翻译按钮区域 -->
                <div class="w-12 flex flex-col items-center justify-center">
                    <el-button type="primary" :loading="translating"
                        :disabled="!selectedImage || !translateConfig.vendor || !translateConfig.from || !translateConfig.to"
                        @click="handleTranslate" class="w-16 h-16 rounded-full">
                        <template v-if="!translating">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                        </template>
                    </el-button>
                    <span class="text-xs text-gray-500 mt-2 text-center">{{ translating ? '翻译中...' : '开始翻译' }}</span>
                </div>

                <!-- 翻译结果区域 -->
                <div class="flex-1 bg-white rounded-lg border p-4 flex flex-col">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">翻译结果</h3>
                    <div class="flex-1 border border-gray-200 rounded-lg p-4 bg-gray-50">
                        <div v-if="!translationResult && !translating"
                            class="h-full flex items-center justify-center text-gray-400">
                            <div class="text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-300" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                <p class="mt-2 text-sm">翻译结果将显示在这里</p>
                            </div>
                        </div>
                        <div v-else-if="translating" class="h-full flex items-center justify-center">
                            <div class="text-center">
                                <el-icon class="animate-spin text-2xl text-blue-500 mb-2">
                                    <Loading />
                                </el-icon>
                                <p class="text-sm text-gray-600">正在翻译图片内容...</p>
                            </div>
                        </div>
                        <div v-else class="h-full">
                            <!-- 阿里云图片翻译结果（图片URL） -->
                            <div v-if="isImageResult" class="h-full flex flex-col">
                                <div class="flex justify-between items-center mb-3">
                                    <span class="text-sm font-medium text-gray-700">翻译后的图片:</span>
                                    <div class="space-x-2">
                                        <el-button size="small" @click="downloadImage">下载图片</el-button>
                                        <el-button size="small" @click="copyImageUrl">复制链接</el-button>
                                    </div>
                                </div>
                                <div
                                    class="flex-1 bg-white border rounded p-3 flex items-center justify-center overflow-hidden">
                                    <img :src="translatedImageUrl" alt="翻译后的图片"
                                        class="max-w-full max-h-full object-contain rounded shadow-sm"
                                        @error="handleImageError" @load="handleImageLoad">
                                </div>
                                <div class="mt-2 text-xs text-gray-500 text-center">
                                    点击下载按钮保存翻译后的图片
                                </div>
                            </div>

                            <!-- 文本翻译结果 -->
                            <div v-else class="h-full flex flex-col">
                                <div class="flex justify-between items-center mb-3">
                                    <span class="text-sm font-medium text-gray-700">翻译内容:</span>
                                    <el-button size="small" @click="copyResult">复制</el-button>
                                </div>
                                <div class="flex-1 bg-white border rounded p-3 overflow-auto">
                                    <pre class="whitespace-pre-wrap text-sm text-gray-800">{{ translationResult }}</pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { Loading } from '@element-plus/icons-vue'
import { translateImageAPI, getLanguagesAPI, VendorType, type TranslateBaseOptions } from '@/api/translation'
import { $message } from '@/utils/message'
import { IEventType } from '@/constants/enum'

// 响应式数据
const selectedImage = ref<File | null>(null)
const imagePreview = ref<string>('')
const translating = ref(false)
const translationResult = ref<string>('')
const imageLoadError = ref(false)

// 翻译配置
const translateConfig = reactive({
    vendor: VendorType.YOUDAO as VendorType,
    from: 'en',
    to: 'zh'
})

// 语言选项
const sourceLanguages = ref<Array<{ label: string; value: string }>>([])
const targetLanguages = ref<Array<{ label: string; value: string }>>([])

// 图片翻译供应商选项
const imageVendors = computed(() => [
    { label: '有道翻译', value: VendorType.YOUDAO },
    { label: '阿里云翻译', value: VendorType.ALIBABA },
    { label: '百度翻译', value: VendorType.BAIDU }
])

// 判断是否为图片结果（阿里云或百度返回图片URL/base64）
const isImageResult = computed(() => {
    return translationResult.value &&
        (translationResult.value.includes('http') ||
            translationResult.value.includes('https') ||
            translationResult.value.includes('data:image/'))
})

// 提取翻译后的图片URL
const translatedImageUrl = computed(() => {
    if (!isImageResult.value) return ''

    // 检查是否为base64格式（百度翻译）
    if (translationResult.value.includes('data:image/')) {
        const base64Match = translationResult.value.match(/data:image\/[^;]+;base64,[^\s]+/)
        return base64Match ? base64Match[0] : ''
    }

    // 从阿里云响应中提取URL
    const urlMatch = translationResult.value.match(/https?:\/\/[^\s]+/)
    return urlMatch ? urlMatch[0] : ''
})

// 文件选择处理
const handleFileSelect = (event: Event) => {
    const target = event.target as HTMLInputElement
    const file = target.files?.[0]
    if (file) {
        handleImageFile(file)
    }
}

// 拖拽处理
const handleDrop = (event: DragEvent) => {
    event.preventDefault()
    const files = event.dataTransfer?.files
    if (files && files.length > 0) {
        const file = files[0]
        if (file.type.startsWith('image/')) {
            handleImageFile(file)
        } else {
            $message.error('请选择图片文件')
        }
    }
}

// 处理图片文件
const handleImageFile = (file: File) => {
    // 检查文件大小（5MB限制）
    if (file.size > 5 * 1024 * 1024) {
        $message.error('图片文件大小不能超过 5MB')
        return
    }

    // 检查文件类型
    const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/bmp']
    if (!allowedTypes.includes(file.type)) {
        $message.error('不支持的图片格式，请选择 PNG、JPG、GIF 或 BMP 格式的图片')
        return
    }

    selectedImage.value = file

    // 创建预览
    const reader = new FileReader()
    reader.onload = (e) => {
        imagePreview.value = e.target?.result as string
    }
    reader.readAsDataURL(file)

    // 清除之前的翻译结果
    translationResult.value = ''
}

// 清除图片
const clearImage = () => {
    selectedImage.value = null
    imagePreview.value = ''
    translationResult.value = ''

    // 清除文件输入框
    const fileInput = document.getElementById('file-upload') as HTMLInputElement
    if (fileInput) {
        fileInput.value = ''
    }
}

// 供应商变更处理
const handleVendorChange = async () => {
    try {
        await loadLanguages()
    } catch (error) {
        console.error('加载语言列表失败:', error)
    }
}

// 加载语言列表
const loadLanguages = async () => {
    if (!translateConfig.vendor) return

    try {
        const [sourceRes, targetRes] = await Promise.all([
            getLanguagesAPI(translateConfig.vendor, 'source'),
            getLanguagesAPI(translateConfig.vendor, 'target')
        ])

        sourceLanguages.value = Object.entries(sourceRes.data).map(([value, label]) => ({
            label: label as string,
            value
        }))

        targetLanguages.value = Object.entries(targetRes.data).map(([value, label]) => ({
            label: label as string,
            value
        }))

        // 设置默认语言
        if (sourceLanguages.value.length > 0 && !translateConfig.from) {
            translateConfig.from = sourceLanguages.value[0].value
        }
        if (targetLanguages.value.length > 0 && !translateConfig.to) {
            translateConfig.to = targetLanguages.value[0].value
        }

        // 阿里云语言限制检查和提示
        if (translateConfig.vendor === VendorType.ALIBABA) {
            checkAlibabaLanguageSupport()
        }
    } catch (error) {
        console.error('获取语言列表失败:', error)
        $message.error('获取语言列表失败')
    }
}

// 检查阿里云语言支持并提供建议
const checkAlibabaLanguageSupport = () => {
    const currentSourceLang = translateConfig.from
    const supportedSourceLangs = ['zh', 'en']

    if (currentSourceLang && !supportedSourceLangs.includes(currentSourceLang)) {
        $message.warning({
            message: `阿里云翻译不支持 ${sourceLanguages.value.find(lang => lang.value === currentSourceLang)?.label} 作为源语言，建议使用有道翻译`,
            duration: 5000,
            showClose: true
        })

        // 自动切换到有道翻译
        setTimeout(() => {
            if (confirm('是否自动切换到有道翻译？')) {
                translateConfig.vendor = VendorType.YOUDAO
                handleVendorChange()
            }
        }, 1000)
    }
}

// 执行翻译
const handleTranslate = async () => {
    if (!selectedImage.value) {
        $message.error('请先选择图片')
        return
    }

    if (!translateConfig.vendor || !translateConfig.from || !translateConfig.to) {
        $message.error('请完善翻译配置')
        return
    }

    translating.value = true
    translationResult.value = ''

    try {
        // 将图片转换为 base64
        const imageBase64 = await fileToBase64(selectedImage.value)

        // 调用翻译API
        const params: TranslateBaseOptions = {
            from: translateConfig.from,
            to: translateConfig.to,
            vendor: translateConfig.vendor
        }
        console.log('params==>', params);

        const response = await window.ipcRenderer?.invoke(IEventType.TranslateImage, imageBase64, params);

        console.log('response==>', response);

        translationResult.value = response
        $message.success('翻译完成')
    } catch (error: any) {
        console.error('翻译失败:', error)
        // const errorMessage = error?.response?.message || error?.message || '翻译失败，请稍后重试'
        // $message.error(errorMessage)
    } finally {
        translating.value = false
    }
}

// 文件转 base64
const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = () => {
            const result = reader.result as string
            resolve(result)
        }
        reader.onerror = () => {
            reject(new Error('文件读取失败'))
        }
        reader.readAsDataURL(file)
    })
}

// 图片加载成功处理
const handleImageLoad = () => {
    imageLoadError.value = false
    console.log('翻译后的图片加载成功')
}

// 图片加载失败处理
const handleImageError = () => {
    imageLoadError.value = true
    console.error('翻译后的图片加载失败')
    $message.error('图片加载失败，请检查网络连接')
}

// 下载翻译后的图片
const downloadImage = async () => {
    if (!translatedImageUrl.value) {
        $message.error('没有可下载的图片')
        return
    }

    try {
        const link = document.createElement('a')
        link.download = `translated-image-${Date.now()}.jpg`

        // 检查是否为base64格式（百度翻译）
        if (translatedImageUrl.value.startsWith('data:image/')) {
            // 直接使用base64数据
            link.href = translatedImageUrl.value
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
            $message.success('图片下载成功')
        } else {
            // 对于URL格式的图片（阿里云翻译），需要先获取blob
            const response = await fetch(translatedImageUrl.value)
            const blob = await response.blob()
            const url = window.URL.createObjectURL(blob)

            link.href = url
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)

            // 清理blob URL
            window.URL.revokeObjectURL(url)
            $message.success('图片下载成功')
        }
    } catch (error) {
        console.error('图片下载失败:', error)
        // 降级方案：直接打开图片链接
        window.open(translatedImageUrl.value, '_blank')
        $message.info('已在新窗口打开图片，请右键保存')
    }
}

// 复制图片链接
const copyImageUrl = async () => {
    if (!translatedImageUrl.value) {
        $message.error('没有可复制的图片链接')
        return
    }

    try {
        await navigator.clipboard.writeText(translatedImageUrl.value)
        $message.success('图片链接复制成功')
    } catch (error) {
        console.error('复制失败:', error)
        // 降级方案
        try {
            const textArea = document.createElement('textarea')
            textArea.value = translatedImageUrl.value
            document.body.appendChild(textArea)
            textArea.select()
            document.execCommand('copy')
            document.body.removeChild(textArea)
            $message.success('图片链接复制成功')
        } catch (fallbackError) {
            $message.error('复制失败')
        }
    }
}

// 复制结果
const copyResult = async () => {
    if (!translationResult.value) {
        $message.warning('没有可复制的内容')
        return
    }

    try {
        await navigator.clipboard.writeText(translationResult.value)
        $message.success('复制成功')
    } catch (error) {
        console.error('复制失败:', error)
        // 降级方案：使用传统方法复制
        try {
            const textArea = document.createElement('textarea')
            textArea.value = translationResult.value
            document.body.appendChild(textArea)
            textArea.select()
            document.execCommand('copy')
            document.body.removeChild(textArea)
            $message.success('复制成功')
        } catch (fallbackError) {
            $message.error('复制失败')
        }
    }
}

// 监听源语言变化，提供智能建议
watch(() => translateConfig.from, (newLang) => {
    if (translateConfig.vendor === VendorType.ALIBABA && newLang) {
        const supportedSourceLangs = ['zh', 'en']
        if (!supportedSourceLangs.includes(newLang)) {
            const langLabel = sourceLanguages.value.find(lang => lang.value === newLang)?.label || newLang
            $message.warning({
                message: `阿里云翻译不支持 ${langLabel} 作为源语言，建议切换到有道翻译`,
                duration: 4000,
                showClose: true
            })
        }
    }
})

// 组件挂载时初始化
onMounted(async () => {
    await loadLanguages()
})
</script>