


import Alimt20181012, * as $Alimt20181012 from '@alicloud/alimt20181012';
import * as $OpenApi from '@alicloud/openapi-client';
import * as $Util from '@alicloud/tea-util';
import { ITranslationRoute } from '../../interface';
import { storeManager } from '../../modules/store';
import { TranslationType, VendorType } from '../../enums';

class AlibabaTranslationService {

    /**
   * 获取所有可用的阿里云图片翻译线路
   */
    async getAvailableAlibabaImageRoutes(): Promise<ITranslationRoute[]> {
        const routes = await storeManager.getTranslateRoutes();
        return routes.filter(
            (route) =>
                route.vendor === VendorType.ALIBABA &&
                route.type === TranslationType.IMAGE &&
                route.isActive,
        );
    }

    /**
     * 创建阿里云客户端
     */
    private createClient(accessKeyId: string, accessKeySecret: string): Alimt20181012 {
        const config = new $OpenApi.Config({
            accessKeyId,
            accessKeySecret,
            regionId: 'cn-hangzhou',
        });
        // 设置访问的域名
        config.endpoint = 'mt.cn-hangzhou.aliyuncs.com';
        return new Alimt20181012(config);
    }

    /**
     * 将语言代码转换为阿里云支持的格式
     */
    private convertLanguageCode(langCode: string): string {
        const languageMap: { [key: string]: string } = {
            zh: 'zh',
            'zh-CN': 'zh',
            'zh-CHS': 'zh',
            en: 'en',
            ja: 'ja',
            ko: 'ko',
            fr: 'fr',
            es: 'es',
            it: 'it',
            de: 'de',
            tr: 'tr',
            ru: 'ru',
            pt: 'pt',
            vi: 'vi',
            id: 'id',
            th: 'th',
            ms: 'ms',
            ar: 'ar',
            hi: 'hi',
        };
        return languageMap[langCode] || langCode;
    }

    /**
     * 执行图片翻译
     */
    async translateImage(
        imageBase64: string,
        from: string,
        to: string,
        route: ITranslationRoute,
    ): Promise<string | null> {
        try {

            const client = this.createClient(route.apiKey, route.apiSecret || '');

            // 转换语言代码
            const sourceLanguage = this.convertLanguageCode(from);
            const targetLanguage = this.convertLanguageCode(to);

            // 阿里云图片翻译特殊限制：仅支持原图为中/英文的图片翻译成其他语言
            if (sourceLanguage !== 'zh' && sourceLanguage !== 'en') {
                return `阿里云图片翻译仅支持中文(zh)或英文(en)作为源语言，当前源语言: ${sourceLanguage}。请使用有道翻译进行其他语言的图片翻译。`
            }

            // 验证必需参数
            if (!sourceLanguage || !targetLanguage) {
                return `语言代码无效: from=${from}, to=${to}, sourceLanguage=${sourceLanguage}, targetLanguage=${targetLanguage}`
            }

            // 移除 data:image/xxx;base64, 前缀（如果存在）
            const base64Data = imageBase64.replace(/^data:image\/[a-z]+;base64,/, '');

            // 验证Base64数据
            if (!base64Data || base64Data.length === 0) {
                throw new Error('Base64图片数据为空');
            }

            // 验证Base64格式
            const base64Regex = /^[A-Za-z0-9+\/]*={0,2}$/;
            if (!base64Regex.test(base64Data)) {
                throw new Error('Base64数据格式无效');
            }

            // 检查图片大小（阿里云限制10MB）
            const imageSizeBytes = (base64Data.length * 3) / 4;
            const imageSizeMB = imageSizeBytes / (1024 * 1024);
            if (imageSizeMB > 10) {
                throw new Error(`图片大小超出限制: ${imageSizeMB.toFixed(2)}MB > 10MB`);
            }

            const translateImageRequest = new $Alimt20181012.TranslateImageRequest({
                imageBase64: base64Data,
                sourceLanguage: sourceLanguage,
                targetLanguage: targetLanguage,
                field: 'general', // 改回general，e-commerce可能需要特殊权限
            });

            const runtime = new $Util.RuntimeOptions({
                autoretry: false,
                ignoreSSL: false,
                maxIdleConns: 50,
                maxIdleTimeMillis: 5000,
                keepAliveDurationMillis: 5000,
                maxRequests: 100,
                maxRequestsPerHost: 100,
                readTimeout: 30000,
                connectTimeout: 30000,
            });

            const response = await client.translateImageWithOptions(translateImageRequest, runtime);

            // 详细的响应日志
            console.log('阿里云API完整响应:', {
                statusCode: response.statusCode,
                headers: response.headers,
                bodyType: typeof response.body,
                bodyKeys: response.body ? Object.keys(response.body) : 'null',
                fullBody: response.body?.data?.finalImageUrl,
            });

            // 尝试多种可能的响应结构
            let message: string | undefined, data: any;

            // 检查其他可能的成功标识
            if (response.statusCode === 200) {
                const imageUrl = response.body?.data?.finalImageUrl || null;
                const result = `图片翻译完成。翻译后的图片URL: ${imageUrl}`;
                console.log(result);
                return imageUrl;
            }

            const errorMsg = message || '阿里云API响应格式异常';
            console.error('阿里云图片翻译失败:', {
                errorMsg,
                responseStatus: response.statusCode,
                hasBody: !!response.body,
                bodyType: typeof response.body,
            });

            // 如果响应结构完全异常，提供更详细的错误信息
            if (!response.body || typeof response.body !== 'object') {
                console.error('阿里云API返回了无效的响应结构');
                return null;
            }

            return null;
        } catch (error: any) {
            console.error('fail:', {
                message: error.message,
                code: error.code,
                data: error.data,
                statusCode: error.statusCode,
                requestId: error.requestId,
            });
            return null;
        }
    }

    /**
     * 智能图片翻译（失败时自动切换线路）
     */
    async smartTranslateImage(
        imageBase64: string,
        from: string,
        to: string,
    ): Promise<string | null> {
        const routes = await this.getAvailableAlibabaImageRoutes();

        if (routes.length === 0) {
            return '没有可用的翻译线路';
        }

        // 尝试每条线路，直到成功或全部失败
        for (const route of routes) {
            const result = await this.translateImage(imageBase64, from, to, route);
            if (result !== null) {
                return result;
            }
        }

        return '没有可用的翻译线路';
    }
}

export default new AlibabaTranslationService();