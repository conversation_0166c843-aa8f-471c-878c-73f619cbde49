import Router from '@koa/router';
import { ClientMembershipController } from '../../controllers/client/membership.controller';
import { requireLogin } from '../../middleware/login-required.middleware';
import { requirePermission } from '../../middleware/permission.middleware';

const router = new Router({
  prefix: '/api/client/membership',
});

// 创建控制器实例
const membershipController = new ClientMembershipController();

// 会员商品相关路由
router.get('/products', membershipController.getProductList);
router.get('/status', requireLogin, membershipController.getMembershipStatus);
router.post('/orders', requireLogin, membershipController.createOrder);
router.get('/orders', requireLogin, membershipController.getOrderList);
router.post('/orders/pay', requireLogin, membershipController.payOrder);
router.post('/payment/notify', membershipController.paymentNotify);
router.post('/check-permission', requireLogin, membershipController.checkPermission);

// 例子：需要权限的路由
router.get('/premium-feature', requireLogin, requirePermission('premium.feature'), async (ctx) => {
  ctx.body = {
    code: 0,
    data: {
      message: '这是高级会员功能',
    },
  };
});

export default router;
