import * as utility from 'utility';
import {
  ZPAY_PID,
  ZPAY_KEY,
  ZPAY_BASE_URL,
  ZPAY_NOTIFY_URL,
  ZPAY_RETURN_URL,
  ZPAY_SITENAME,
} from '../config/z-pay';

export interface ZPayConfig {
  pid: string;
  key: string;
  baseUrl: string;
  notifyUrl: string;
  returnUrl: string;
  sitename: string;
}

export interface CreatePaymentOptions {
  orderNo: string;
  money: number; // 金额，单位：元
  name: string; // 商品名称
  type: 'alipay' | 'wxpay' | 'qqpay' | 'tenpay'; // 支付方式
}

export interface PaymentNotifyData {
  pid: string;
  trade_no: string;
  out_trade_no: string;
  type: string;
  name: string;
  money: string;
  trade_status: string;
  sign: string;
  sign_type: string;
}

/**
 * ZPay支付服务
 * 根据zpay文档实现支付功能
 */
export class ZPayService {
  private config: ZPayConfig;

  constructor(config: ZPayConfig) {
    this.config = config;
  }

  /**
   * 参数进行排序拼接字符串
   * @param params 参数对象
   * @returns 排序后的参数字符串
   */
  private getVerifyParams(params: Record<string, any>): string | null {
    const sPara: [string, any][] = [];

    if (!params) return null;

    for (const key in params) {
      if (!params[key] || key === 'sign' || key === 'sign_type') {
        continue;
      }
      sPara.push([key, params[key]]);
    }

    sPara.sort();

    let prestr = '';
    for (let i = 0; i < sPara.length; i++) {
      const obj = sPara[i];
      if (i === sPara.length - 1) {
        prestr = prestr + obj[0] + '=' + obj[1] + '';
      } else {
        prestr = prestr + obj[0] + '=' + obj[1] + '&';
      }
    }

    return prestr;
  }

  /**
   * 生成签名
   * @param params 参数对象
   * @returns MD5签名
   */
  private generateSign(params: Record<string, any>): string {
    const str = this.getVerifyParams(params);
    if (!str) {
      throw new Error('参数为空，无法生成签名');
    }

    // MD5加密--进行签名
    return utility.md5(str + this.config.key);
  }

  /**
   * 验证签名
   * @param params 参数对象
   * @returns 签名是否正确
   */
  private verifySign(params: Record<string, any>): boolean {
    const receivedSign = params.sign;
    if (!receivedSign) {
      return false;
    }

    const calculatedSign = this.generateSign(params);
    return receivedSign === calculatedSign;
  }

  /**
   * 创建支付链接
   * @param options 支付选项
   * @returns 支付链接
   */
  createPaymentUrl(options: CreatePaymentOptions): string {
    const { orderNo, money, name, type } = options;

    // 构建支付参数
    const params = {
      pid: this.config.pid,
      money: money.toFixed(2), // 确保金额格式正确
      name,
      notify_url: this.config.notifyUrl,
      out_trade_no: orderNo,
      return_url: this.config.returnUrl,
      sitename: this.config.sitename,
      type,
    };

    // 生成签名
    const sign = this.generateSign(params);

    // 构建参数字符串
    const paramStr = this.getVerifyParams(params);
    if (!paramStr) {
      throw new Error('构建参数字符串失败');
    }

    // 返回完整的支付链接
    return `${this.config.baseUrl}/submit.php?${paramStr}&sign=${sign}&sign_type=MD5`;
  }

  /**
   * 验证支付回调通知
   * @param notifyData 回调数据
   * @returns 验证结果和订单信息
   */
  verifyNotify(notifyData: PaymentNotifyData): {
    isValid: boolean;
    orderNo?: string;
    tradeNo?: string;
    money?: number;
    isSuccess?: boolean;
  } {
    // 验证签名
    const isValid = this.verifySign(notifyData);

    if (!isValid) {
      return { isValid: false };
    }

    // 检查支付状态
    const isSuccess = notifyData.trade_status === 'TRADE_SUCCESS';

    return {
      isValid: true,
      orderNo: notifyData.out_trade_no,
      tradeNo: notifyData.trade_no,
      money: parseFloat(notifyData.money),
      isSuccess,
    };
  }

  /**
   * 创建支付配置（从环境变量读取）
   */
  static createFromEnv(): ZPayService {
    const config: ZPayConfig = {
      pid: ZPAY_PID || '',
      key: ZPAY_KEY || '',
      baseUrl: ZPAY_BASE_URL || 'https://z-pay.cn',
      notifyUrl: ZPAY_NOTIFY_URL || '',
      returnUrl: ZPAY_RETURN_URL || '',
      sitename: ZPAY_SITENAME || '巨鲸AI',
    };

    // 验证必要配置
    if (!config.pid || !config.key) {
      throw new Error('ZPay配置不完整，请检查环境变量 ZPAY_PID 和 ZPAY_KEY');
    }

    if (!config.notifyUrl) {
      throw new Error('ZPay配置不完整，请设置环境变量 ZPAY_NOTIFY_URL');
    }

    return new ZPayService(config);
  }

  /**
   * 获取支付方式列表
   */
  static getPaymentMethods(): Array<{ value: string; label: string }> {
    return [
      { value: 'alipay', label: '支付宝' },
      { value: 'wxpay', label: '微信支付' },
    ];
  }
}
