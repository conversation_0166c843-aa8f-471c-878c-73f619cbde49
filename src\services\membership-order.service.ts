import { Repository } from 'typeorm';
import { AppDataSource } from '../config/database';
import { BaseService } from './base.service';
import { MembershipOrder } from '../entities/MembershipOrder';
import { MembershipProduct } from '../entities/MembershipProduct';
import { Client } from '../entities/Client';
import { ClientMembershipService } from './client-membership.service';

/**
 * 会员订单服务
 */
export class MembershipOrderService extends BaseService<MembershipOrder> {
  private membershipProductRepository: Repository<MembershipProduct>;
  private clientMembershipService: ClientMembershipService;

  constructor() {
    super(AppDataSource.getRepository(MembershipOrder));
    this.membershipProductRepository = AppDataSource.getRepository(MembershipProduct);
    this.clientMembershipService = new ClientMembershipService();
  }

  /**
   * 创建会员订单
   */
  async createOrder(clientId: string, productId: string): Promise<MembershipOrder> {
    // 获取会员产品
    const product = await this.membershipProductRepository.findOne({
      where: { id: productId },
    });

    if (!product) {
      throw new Error('会员商品不存在');
    }

    // 生成订单号
    const orderNo = this.generateOrderNo();

    // 创建订单
    const order = await this.create({
      orderNo,
      client: { id: clientId } as Client,
      membershipProduct: product,
      amount: product.price,
      duration: product.duration,
      status: 'pending',
    });

    return order;
  }

  /**
   * 处理订单支付成功
   */
  async handlePaymentSuccess(
    orderNo: string,
    paymentMethod: string,
    transactionId: string,
  ): Promise<MembershipOrder> {
    // 查找订单
    const order = await this.repository.findOne({
      where: { orderNo },
      relations: ['client', 'membershipProduct'],
    });

    if (!order) {
      throw new Error('订单不存在');
    }

    if (order.status === 'paid') {
      return order; // 订单已支付，直接返回
    }

    // 更新订单状态
    await this.update(order.id, {
      status: 'paid',
      paymentMethod,
      transactionId,
      paidAt: new Date(),
    });

    // 添加会员时长
    await this.clientMembershipService.addMembership(
      order.client.id,
      order.membershipProduct.id,
      order.duration,
    );

    return {
      ...order,
      status: 'paid',
      paymentMethod,
      transactionId,
      paidAt: new Date(),
    };
  }

  /**
   * 取消订单
   */
  async cancelOrder(orderNo: string): Promise<boolean> {
    // 查找订单
    const order = await this.repository.findOne({
      where: { orderNo },
    });

    if (!order) {
      throw new Error('订单不存在');
    }

    if (order.status === 'paid') {
      throw new Error('已支付的订单无法取消');
    }

    // 更新订单状态
    return this.update(order.id, {
      status: 'cancelled',
    });
  }

  /**
   * 生成订单号
   */
  private generateOrderNo(): string {
    const now = new Date();
    const year = now.getFullYear().toString().substr(2);
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hour = String(now.getHours()).padStart(2, '0');
    const minute = String(now.getMinutes()).padStart(2, '0');
    const second = String(now.getSeconds()).padStart(2, '0');
    const random = Math.floor(Math.random() * 1000)
      .toString()
      .padStart(3, '0');

    return `M${year}${month}${day}${hour}${minute}${second}${random}`;
  }

  /**
   * 获取用户订单列表
   */
  async getClientOrders(clientId: string, status?: string): Promise<MembershipOrder[]> {
    const where: any = {
      client: { id: clientId } as Client,
    };

    if (status) {
      where.status = status;
    }

    return this.repository.find({
      where,
      relations: ['membershipProduct'],
      order: { createdAt: 'DESC' },
    });
  }
}
