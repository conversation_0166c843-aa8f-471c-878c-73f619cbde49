# 客户端会员模块 API 文档

## 概述

客户端会员模块提供会员相关功能，包括会员商品查看、订单管理、支付处理、权限检查等。

**基础路径:** `/api/client/membership`

## 接口列表

### 1. 获取会员商品列表

获取所有可用的会员商品列表。

- **URL:** `/api/client/membership/products`
- **方法:** `GET`
- **权限:** 公开接口

- **成功响应:**

```json
{
  "code": 0,
  "message": "获取会员商品列表成功",
  "data": [
    {
      "id": "product_001",
      "name": "基础会员",
      "description": "基础会员套餐，包含基础翻译功能",
      "price": 99.00,
      "duration": 30,
      "sort": 1,
      "isActive": true,
      "iconUrl": "https://example.com/basic-icon.png",
      "permissions": [
        {
          "id": "perm_001",
          "name": "基础翻译",
          "key": "basic.translation",
          "description": "基础翻译功能权限"
        }
      ]
    },
    {
      "id": "product_002",
      "name": "高级会员",
      "description": "高级会员套餐，包含所有翻译功能",
      "price": 199.00,
      "duration": 30,
      "sort": 2,
      "isActive": true,
      "iconUrl": "https://example.com/premium-icon.png",
      "permissions": [
        {
          "id": "perm_001",
          "name": "基础翻译",
          "key": "basic.translation",
          "description": "基础翻译功能权限"
        },
        {
          "id": "perm_002",
          "name": "高级翻译",
          "key": "premium.translation",
          "description": "高级翻译功能权限"
        }
      ]
    }
  ]
}
```

## 需要认证的接口

以下接口需要在请求头中携带认证令牌：`Authorization: Bearer {token}`

### 2. 获取会员状态

获取当前用户的会员状态信息。

- **URL:** `/api/client/membership/status`
- **方法:** `GET`
- **权限:** 需要用户认证

- **成功响应:**

```json
{
  "code": 0,
  "message": "获取会员状态成功",
  "data": {
    "isMember": true,
    "membershipType": "高级会员",
    "expiresAt": "2023-02-01T00:00:00.000Z",
    "daysRemaining": 15,
    "permissions": [
      "basic.translation",
      "premium.translation"
    ]
  }
}
```

### 3. 创建订单

创建会员商品订单。

- **URL:** `/api/client/membership/orders`
- **方法:** `POST`
- **权限:** 需要用户认证
- **请求体:**

```json
{
  "productId": "product_002"
}
```

**请求参数说明:**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| productId | string | 是 | 会员商品ID |

- **成功响应:**

```json
{
  "code": 0,
  "message": "创建订单成功",
  "data": {
    "id": "order_001",
    "orderNo": "ORD20230101001",
    "productId": "product_002",
    "productName": "高级会员",
    "price": 199.00,
    "status": "pending",
    "clientId": "user_001",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

### 4. 获取订单列表

获取当前用户的订单列表。

- **URL:** `/api/client/membership/orders`
- **方法:** `GET`
- **权限:** 需要用户认证
- **查询参数:**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| page | number | 否 | 页码，默认为1 |
| pageSize | number | 否 | 每页条数，默认为10 |
| status | string | 否 | 订单状态筛选 |

- **成功响应:**

```json
{
  "code": 0,
  "message": "获取订单列表成功",
  "data": {
    "items": [
      {
        "id": "order_001",
        "orderNo": "ORD20230101001",
        "productId": "product_002",
        "productName": "高级会员",
        "price": 199.00,
        "status": "paid",
        "paymentMethod": "alipay",
        "transactionId": "TX1672531200000",
        "createdAt": "2023-01-01T00:00:00.000Z",
        "updatedAt": "2023-01-01T00:00:00.000Z"
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10,
    "totalPages": 1
  }
}
```

### 5. 支付订单

处理订单支付（模拟支付）。

- **URL:** `/api/client/membership/orders/pay`
- **方法:** `POST`
- **权限:** 需要用户认证
- **请求体:**

```json
{
  "orderNo": "ORD20230101001",
  "paymentMethod": "alipay"
}
```

**请求参数说明:**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| orderNo | string | 是 | 订单号 |
| paymentMethod | string | 是 | 支付方式（alipay/wechat/bank） |

- **成功响应:**

```json
{
  "code": 0,
  "message": "支付成功",
  "data": {
    "id": "order_001",
    "orderNo": "ORD20230101001",
    "productId": "product_002",
    "productName": "高级会员",
    "price": 199.00,
    "status": "paid",
    "paymentMethod": "alipay",
    "transactionId": "TX1672531200000",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

### 6. 检查权限

检查当前用户是否拥有指定权限。

- **URL:** `/api/client/membership/check-permission`
- **方法:** `POST`
- **权限:** 需要用户认证
- **请求体:**

```json
{
  "permissionKey": "premium.translation"
}
```

**请求参数说明:**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| permissionKey | string | 是 | 权限标识 |

- **成功响应:**

```json
{
  "code": 0,
  "message": "检查权限成功",
  "data": {
    "hasPermission": true
  }
}
```

## 订单状态说明

| 状态值 | 描述 |
|--------|------|
| pending | 待支付 |
| paid | 已支付 |
| cancelled | 已取消 |
| refunded | 已退款 |

## 支付方式说明

| 支付方式 | 描述 |
|----------|------|
| alipay | 支付宝 |
| wechat | 微信支付 |
| bank | 银行卡 |

## 错误码说明

| 错误码 | 描述 |
|--------|------|
| 400 | 请求参数错误或验证失败 |
| 401 | 未授权或认证失败 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 注意事项

1. 会员商品价格单位为元
2. 会员有效期以天为单位
3. 支付成功后会自动激活会员权限
4. 订单支付目前为模拟支付，实际环境需要对接真实支付接口
5. 权限检查用于前端功能控制，后端接口也会进行权限验证
6. 会员到期后权限会自动失效
